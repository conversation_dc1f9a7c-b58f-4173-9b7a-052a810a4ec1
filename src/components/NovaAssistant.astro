---
// Nova AI Assistant Component - Cross-dimensional AI being
---

<div class="nova-container">
  <div class="nova-avatar" id="novaAvatar">
    <img src="/nova.tiff" alt="Nova - Cross-dimensional AI" class="nova-image" />
    <div class="nova-aura"></div>
    <div class="dimensional-rings">
      <div class="ring ring-1"></div>
      <div class="ring ring-2"></div>
      <div class="ring ring-3"></div>
    </div>
  </div>

  <div class="nova-tooltip" id="novaTooltip">
    <div class="tooltip-content">
      <p class="nova-greeting"><PERSON><PERSON><PERSON>, reality engineer.</p>
      <p class="nova-subtitle">I am Nova, your cross-dimensional guide.</p>
      <div class="tooltip-arrow"></div>
    </div>
  </div>
</div>

<style>
  .nova-container {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    z-index: 1000;
    cursor: pointer;
  }

  .nova-avatar {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: float 6s ease-in-out infinite;
  }

  .nova-avatar:hover {
    transform: scale(1.1) translateY(-5px);
  }

  .nova-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    filter: brightness(1.1) contrast(1.2) saturate(1.3);
  }

  .nova-aura {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(168, 178, 255, 0.3) 0%, transparent 70%);
    animation: pulse-aura 4s ease-in-out infinite;
    pointer-events: none;
  }

  .dimensional-rings {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }

  .ring {
    position: absolute;
    border: 1px solid rgba(168, 178, 255, 0.4);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
  }

  .ring-1 {
    width: 100px;
    height: 100px;
    top: -50px;
    left: -50px;
    animation-duration: 15s;
  }

  .ring-2 {
    width: 120px;
    height: 120px;
    top: -60px;
    left: -60px;
    animation-duration: 25s;
    animation-direction: reverse;
  }

  .ring-3 {
    width: 140px;
    height: 140px;
    top: -70px;
    left: -70px;
    animation-duration: 35s;
  }

  .nova-tooltip {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: 1rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    pointer-events: none;
  }

  .nova-tooltip.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(-10px);
  }

  .tooltip-content {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(168, 178, 255, 0.3);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    min-width: 250px;
    backdrop-filter: blur(10px);
    position: relative;
  }

  .nova-greeting {
    color: #a8b2ff;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
  }

  .nova-subtitle {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .tooltip-arrow {
    position: absolute;
    bottom: -6px;
    right: 2rem;
    width: 12px;
    height: 12px;
    background: rgba(0, 0, 0, 0.9);
    border-right: 1px solid rgba(168, 178, 255, 0.3);
    border-bottom: 1px solid rgba(168, 178, 255, 0.3);
    transform: rotate(45deg);
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes pulse-aura {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.6;
      transform: scale(1.1);
    }
  }

  @keyframes rotate {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  @media (max-width: 768px) {
    .nova-container {
      bottom: 1rem;
      right: 1rem;
    }

    .nova-avatar {
      width: 60px;
      height: 60px;
    }

    .tooltip-content {
      min-width: 200px;
      padding: 0.8rem 1rem;
    }

    .ring-1 {
      width: 80px;
      height: 80px;
      top: -40px;
      left: -40px;
    }

    .ring-2 {
      width: 100px;
      height: 100px;
      top: -50px;
      left: -50px;
    }

    .ring-3 {
      width: 120px;
      height: 120px;
      top: -60px;
      left: -60px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const novaAvatar = document.getElementById('novaAvatar');
    const novaTooltip = document.getElementById('novaTooltip');
    let tooltipTimeout: NodeJS.Timeout;

    if (novaAvatar && novaTooltip) {
      novaAvatar.addEventListener('mouseenter', () => {
        clearTimeout(tooltipTimeout);
        novaTooltip.classList.add('visible');
      });

      novaAvatar.addEventListener('mouseleave', () => {
        tooltipTimeout = setTimeout(() => {
          novaTooltip.classList.remove('visible');
        }, 300);
      });

      novaAvatar.addEventListener('click', () => {
        // Future: Open chat interface with Nova
        console.log('Nova chat interface - Coming soon...');
      });
    }
  });
</script>
