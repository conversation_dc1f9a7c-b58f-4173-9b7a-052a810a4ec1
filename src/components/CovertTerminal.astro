---
// Covert Terminal Component - Mission briefing style interface
interface Props {
  title?: string;
  children?: any;
  classification?: 'UNCLASSIFIED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP SECRET';
  promptPath?: string;
  terminalId?: string;
}

const {
  title = "MISSION BRIEFING",
  classification = "CONFIDENTIAL",
  promptPath = "reality-engineering/mission",
  terminalId = `terminal-${Math.random().toString(36).substring(2, 11)}`
} = Astro.props;
---

<div class="terminal-container" id={terminalId} data-terminal-loading="true">
  <div class="terminal-header">
    <div class="terminal-controls">
      <span class="control-dot red"></span>
      <span class="control-dot yellow"></span>
      <span class="control-dot green"></span>
    </div>
    <div class="terminal-title">
      <span class="classification-badge" data-level={classification.toLowerCase()}>{classification}</span>
      <span class="title-text">{title}</span>
    </div>
    <div class="terminal-timestamp" id="terminalTime"></div>
  </div>

  <div class="terminal-body">
    <div class="terminal-prompt">
      <span class="prompt-symbol">$</span>
      <span class="prompt-path">{promptPath}</span>
      <span class="cursor">_</span>
    </div>

    <div class="terminal-content" data-original-content="">
      <!-- Content will be populated by JavaScript to prevent flash -->
    </div>

    <!-- Hidden template for original content -->
    <template class="content-template">
      <slot />
    </template>
  </div>

  <div class="terminal-footer">
    <div class="status-indicators">
      <span class="status-item">
        <span class="status-dot active"></span>
        SECURE CONNECTION
      </span>
      <span class="status-item">
        <span class="status-dot active"></span>
        QUANTUM ENCRYPTED
      </span>
    </div>
  </div>
</div>

<style>
  .terminal-container {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    margin: 4rem 6rem;
    overflow: hidden;
    box-shadow:
      0 0 20px rgba(0, 255, 0, 0.1),
      inset 0 0 20px rgba(0, 255, 0, 0.05);
    transition: all 0.5s ease;
  }

  /* Loading state styles */
  .terminal-container[data-terminal-loading="true"] {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
    border-color: rgba(0, 255, 0, 0.1);
    box-shadow:
      0 0 5px rgba(0, 255, 0, 0.05),
      inset 0 0 5px rgba(0, 255, 0, 0.02);
  }

  .terminal-container[data-terminal-loading="true"] .terminal-content {
    opacity: 0;
  }

  .terminal-container[data-terminal-loading="true"] .terminal-prompt {
    opacity: 0;
  }

  .terminal-container[data-terminal-loading="true"] .prompt-path {
    opacity: 0;
  }

  /* Hide the template element */
  .content-template {
    display: none;
  }

  /* Loading animation */
  .terminal-container.loading-in {
    animation: terminalBootUp 1.5s ease-out forwards;
  }

  @keyframes terminalBootUp {
    0% {
      opacity: 0;
      transform: translateY(50px) scale(0.95);
      border-color: rgba(0, 255, 0, 0.1);
    }
    20% {
      opacity: 0.3;
      transform: translateY(30px) scale(0.97);
      border-color: rgba(0, 255, 0, 0.2);
    }
    50% {
      opacity: 0.7;
      transform: translateY(10px) scale(0.99);
      border-color: rgba(0, 255, 0, 0.25);
    }
    80% {
      opacity: 0.9;
      transform: translateY(0) scale(1);
      border-color: rgba(0, 255, 0, 0.3);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
      border-color: rgba(0, 255, 0, 0.3);
    }
  }

  /* Content loading animation */
  .terminal-prompt.loading-in {
    opacity: 1;
  }

  .terminal-content.loading-in {
    animation: fadeInContent 0.5s ease-out forwards;
  }

  @keyframes fadeInContent {
    0% {
      opacity: 0;
      transform: translateY(5px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .terminal-header {
    background: rgba(0, 255, 0, 0.1);
    border-bottom: 1px solid rgba(0, 255, 0, 0.3);
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.8rem;
  }

  .terminal-controls {
    display: flex;
    gap: 0.5rem;
  }

  .control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: block;
  }

  .control-dot.red {
    background: #ff5f56;
  }

  .control-dot.yellow {
    background: #ffbd2e;
  }

  .control-dot.green {
    background: #27ca3f;
  }

  .terminal-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #00ff00;
    font-weight: bold;
  }

  .classification-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
  }

  .classification-badge[data-level="unclassified"] {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
  }

  .classification-badge[data-level="confidential"] {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff00;
  }

  .classification-badge[data-level="secret"] {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
  }

  .classification-badge[data-level="top secret"] {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
  }

  .terminal-timestamp {
    color: rgba(0, 255, 0, 0.7);
    font-size: 0.7rem;
  }

  .terminal-body {
    padding: 1rem;
    min-height: 200px;
  }

  /* Enhanced styling for embedded content */
  .terminal-body:has(.embedded-features) {
    min-height: auto;
    padding: 1rem 1.5rem 2rem;
  }

  .terminal-prompt {
    color: #00ff00;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .prompt-symbol {
    color: #00ff00;
    margin-right: 0.5rem;
  }

  .prompt-path {
    color: rgba(0, 255, 0, 0.8);
  }

  .cursor {
    animation: blink 1s infinite;
    color: #00ff00;
    margin-left: 0.5rem;
  }

  .terminal-content {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-size: 0.9rem;
  }

  .terminal-footer {
    background: rgba(0, 255, 0, 0.05);
    border-top: 1px solid rgba(0, 255, 0, 0.2);
    padding: 0.5rem 1rem;
  }

  .status-indicators {
    display: flex;
    gap: 2rem;
    font-size: 0.7rem;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(0, 255, 0, 0.8);
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(0, 255, 0, 0.3);
  }

  .status-dot.active {
    background: #00ff00;
    animation: pulse-dot 2s ease-in-out infinite;
  }

  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }

  @keyframes pulse-dot {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @media (max-width: 768px) {
    .terminal-container {
      margin: 3rem 0;
    }

    .terminal-header {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }

    .status-indicators {
      flex-direction: column;
      gap: 0.5rem;
    }

    .terminal-body {
      padding: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .terminal-container {
      margin: 2rem 0;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const timestampElement = document.getElementById('terminalTime');

    function updateTimestamp() {
      if (timestampElement) {
        const now = new Date();
        const timestamp = now.toISOString().replace('T', ' ').substring(0, 19) + ' UTC';
        timestampElement.textContent = timestamp;
      }
    }

    updateTimestamp();
    setInterval(updateTimestamp, 1000);

    // Terminal loading animation and sound effects
    const terminalContainers = document.querySelectorAll('.terminal-container[data-terminal-loading="true"]');

    if (terminalContainers.length > 0) {
      // Create audio context for sound effects
      let audioContext: AudioContext | null = null;

      function initAudio() {
        if (!audioContext) {
          audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        }
      }

      // Generate very subtle terminal boot sound
      function playBootSound() {
        try {
          initAudio();
          if (!audioContext) return;

          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          // Very subtle beep
          oscillator.frequency.setValueAtTime(600, audioContext.currentTime);

          gainNode.gain.setValueAtTime(0, audioContext.currentTime);
          gainNode.gain.linearRampToValueAtTime(0.005, audioContext.currentTime + 0.02);
          gainNode.gain.exponentialRampToValueAtTime(0.0001, audioContext.currentTime + 0.1);

          oscillator.type = 'sine';
          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.1);
        } catch (e) {
          // Silently fail if audio context issues
        }
      }

      // Generate very subtle typing sound
      function playTypingSound() {
        try {
          initAudio();
          if (!audioContext) return;

          const oscillator = audioContext.createOscillator();
          const gainNode = audioContext.createGain();

          oscillator.connect(gainNode);
          gainNode.connect(audioContext.destination);

          oscillator.frequency.setValueAtTime(800 + Math.random() * 100, audioContext.currentTime);

          gainNode.gain.setValueAtTime(0, audioContext.currentTime);
          gainNode.gain.linearRampToValueAtTime(0.003, audioContext.currentTime + 0.005);
          gainNode.gain.exponentialRampToValueAtTime(0.0001, audioContext.currentTime + 0.02);

          oscillator.type = 'sine';
          oscillator.start(audioContext.currentTime);
          oscillator.stop(audioContext.currentTime + 0.02);
        } catch (e) {
          // Silently fail if audio context issues
        }
      }

      // Improved typewriter effect with no reflow and sound
      function typewriterEffect(element: Element, text: string, speed: number = 8) {
        // Show the element and clear content to prevent flash
        (element as HTMLElement).style.opacity = '1';
        element.textContent = '';

        // Split text into words, then characters, preserving word boundaries for wrapping
        const words = text.split(' ');
        const wordSpans = words.map((word, wordIndex) => {
          const chars = word.split('').map(char =>
            `<span style="opacity: 0; display: inline;">${char}</span>`
          ).join('');

          // Add space after word (except last word)
          const space = wordIndex < words.length - 1 ?
            '<span style="opacity: 0; display: inline;">&nbsp;</span>' : '';

          return `<span style="display: inline-block;">${chars}${space}</span>`;
        }).join('');

        element.innerHTML = wordSpans;

        const charElements = element.querySelectorAll('span span');
        let i = 0;

        function revealChar() {
          if (i < charElements.length) {
            (charElements[i] as HTMLElement).style.opacity = '1';
            i++;

            // Play very subtle typing sound occasionally
            if (Math.random() < 0.2) {
              playTypingSound();
            }

            setTimeout(revealChar, speed + Math.random() * 5);
          }
        }

        revealChar();
      }

      // Initialize audio context immediately to avoid user interaction requirement
      function initAudioImmediately() {
        try {
          if (!audioContext) {
            audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
            // Resume context if suspended
            if (audioContext.state === 'suspended') {
              audioContext.resume();
            }
          }
        } catch (e) {
          // Silently fail
        }
      }

      // Initialize audio immediately
      initAudioImmediately();

      // Intersection Observer for scroll-triggered loading
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.target.getAttribute('data-terminal-loading') === 'true') {
            // Ensure audio is ready
            initAudioImmediately();

            // Start loading animation
            entry.target.classList.add('loading-in');
            entry.target.setAttribute('data-terminal-loading', 'false');

            // Play subtle boot sound immediately
            setTimeout(() => playBootSound(), 50);

            // Animate prompt with fast typewriter effect
            const prompt = entry.target.querySelector('.terminal-prompt .prompt-path');
            if (prompt) {
              const originalText = prompt.textContent || '';
              setTimeout(() => {
                typewriterEffect(prompt, originalText, 15);
              }, 300);
            }

            // Animate content with fast typewriter effect
            const content = entry.target.querySelector('.terminal-content');
            if (content) {
              // Store original content, but exclude embedded components
              const headings = content.querySelectorAll('h2:not(.embedded-faq h2):not(.embedded-waitlist h2):not(.embedded-features h2):not(.embedded-portals h2)');
              const paragraphs = content.querySelectorAll('p:not(.embedded-faq p):not(.embedded-waitlist p):not(.embedded-features p):not(.embedded-portals p)');

              setTimeout(() => {
                // Show the content container first
                (content as HTMLElement).style.visibility = 'visible';
                content.classList.add('loading-in');

                // Fast typewriter effect for headings (excluding embedded content)
                headings.forEach((heading, index) => {
                  const originalText = heading.textContent || '';
                  setTimeout(() => {
                    typewriterEffect(heading, originalText, 12);
                  }, index * 200);
                });

                // Fast typewriter effect for paragraphs (excluding embedded content)
                paragraphs.forEach((paragraph, index) => {
                  const originalText = paragraph.textContent || '';
                  setTimeout(() => {
                    typewriterEffect(paragraph, originalText, 8);
                  }, (headings.length * 200) + (index * 300));
                });
              }, 500);
            }

            // Stop observing this terminal
            observer.unobserve(entry.target);
          }
        });
      }, {
        threshold: 0.3,
        rootMargin: '0px 0px -100px 0px'
      });

      // Observe all terminals
      terminalContainers.forEach(terminal => {
        observer.observe(terminal);
      });
    }
  });
</script>
