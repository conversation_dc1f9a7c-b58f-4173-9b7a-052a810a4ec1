---
// Covert Terminal Component - Mission briefing style interface
interface Props {
  title?: string;
  children?: any;
  classification?: 'UNCLASSIFIED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP SECRET';
}

const { title = "MISSION BRIEFING", classification = "CONFIDENTIAL" } = Astro.props;
---

<div class="terminal-container">
  <div class="terminal-header">
    <div class="terminal-controls">
      <span class="control-dot red"></span>
      <span class="control-dot yellow"></span>
      <span class="control-dot green"></span>
    </div>
    <div class="terminal-title">
      <span class="classification-badge" data-level={classification.toLowerCase()}>{classification}</span>
      <span class="title-text">{title}</span>
    </div>
    <div class="terminal-timestamp" id="terminalTime"></div>
  </div>

  <div class="terminal-body">
    <div class="terminal-prompt">
      <span class="prompt-symbol">$</span>
      <span class="prompt-path">reality-engineering/mission</span>
      <span class="cursor">_</span>
    </div>

    <div class="terminal-content">
      <slot />
    </div>
  </div>

  <div class="terminal-footer">
    <div class="status-indicators">
      <span class="status-item">
        <span class="status-dot active"></span>
        SECURE CONNECTION
      </span>
      <span class="status-item">
        <span class="status-dot active"></span>
        QUANTUM ENCRYPTED
      </span>
    </div>
  </div>
</div>

<style>
  .terminal-container {
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    margin: 4rem 6rem;
    overflow: hidden;
    box-shadow:
      0 0 20px rgba(0, 255, 0, 0.1),
      inset 0 0 20px rgba(0, 255, 0, 0.05);
  }

  .terminal-header {
    background: rgba(0, 255, 0, 0.1);
    border-bottom: 1px solid rgba(0, 255, 0, 0.3);
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.8rem;
  }

  .terminal-controls {
    display: flex;
    gap: 0.5rem;
  }

  .control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: block;
  }

  .control-dot.red {
    background: #ff5f56;
  }

  .control-dot.yellow {
    background: #ffbd2e;
  }

  .control-dot.green {
    background: #27ca3f;
  }

  .terminal-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #00ff00;
    font-weight: bold;
  }

  .classification-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
  }

  .classification-badge[data-level="unclassified"] {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
  }

  .classification-badge[data-level="confidential"] {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff00;
  }

  .classification-badge[data-level="secret"] {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
  }

  .classification-badge[data-level="top secret"] {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
  }

  .terminal-timestamp {
    color: rgba(0, 255, 0, 0.7);
    font-size: 0.7rem;
  }

  .terminal-body {
    padding: 1rem;
    min-height: 200px;
  }

  /* Enhanced styling for embedded content */
  .terminal-body:has(.embedded-features) {
    min-height: auto;
    padding: 1rem 1.5rem 2rem;
  }

  .terminal-prompt {
    color: #00ff00;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .prompt-symbol {
    color: #00ff00;
    margin-right: 0.5rem;
  }

  .prompt-path {
    color: rgba(0, 255, 0, 0.8);
  }

  .cursor {
    animation: blink 1s infinite;
    color: #00ff00;
    margin-left: 0.5rem;
  }

  .terminal-content {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-size: 0.9rem;
  }

  .terminal-footer {
    background: rgba(0, 255, 0, 0.05);
    border-top: 1px solid rgba(0, 255, 0, 0.2);
    padding: 0.5rem 1rem;
  }

  .status-indicators {
    display: flex;
    gap: 2rem;
    font-size: 0.7rem;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(0, 255, 0, 0.8);
  }

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(0, 255, 0, 0.3);
  }

  .status-dot.active {
    background: #00ff00;
    animation: pulse-dot 2s ease-in-out infinite;
  }

  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }

  @keyframes pulse-dot {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @media (max-width: 768px) {
    .terminal-container {
      margin: 3rem 0;
    }

    .terminal-header {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }

    .status-indicators {
      flex-direction: column;
      gap: 0.5rem;
    }

    .terminal-body {
      padding: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .terminal-container {
      margin: 2rem 0;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const timestampElement = document.getElementById('terminalTime');

    function updateTimestamp() {
      if (timestampElement) {
        const now = new Date();
        const timestamp = now.toISOString().replace('T', ' ').substring(0, 19) + ' UTC';
        timestampElement.textContent = timestamp;
      }
    }

    updateTimestamp();
    setInterval(updateTimestamp, 1000);
  });
</script>
