<form class="waitlist-form" id="waitlistForm">
	<div class="input-group">
		<input type="tel" id="phone" placeholder="+1 (   )" required pattern="[0-9+() -]*">
		<button type="submit" class="submit-btn">ENGAGE →</button>
	</div>
	<p class="form-note">Opt-in for black-label briefings, live lab invites, and generative growth spells.</p>
</form>

<style>
	.waitlist-form {
		max-width: 500px;
		margin: 2rem auto;
	}

	.input-group {
		display: flex;
		gap: 0.5rem;
	}

	input {
		flex: 1;
		padding: 1rem 1.5rem;
		border-radius: 8px;
		border: 1px solid rgba(168, 178, 255, 0.3);
		background: rgba(0, 0, 0, 0.3);
		color: #fff;
		font-family: 'Space Grotesk', sans-serif;
		font-size: 1rem;
		backdrop-filter: blur(10px);
	}

	input:focus {
		outline: none;
		border-color: rgba(168, 178, 255, 0.8);
		box-shadow: 0 0 0 2px rgba(168, 178, 255, 0.2);
	}

	input::placeholder {
		color: rgba(255, 255, 255, 0.5);
	}

	.submit-btn {
		padding: 1rem 1.5rem;
		border: none;
		border-radius: 8px;
		background: linear-gradient(45deg, #6e7bff, #a8b2ff);
		color: #000;
		font-weight: 400;
		cursor: pointer;
		transition: transform 0.2s, box-shadow 0.2s;
		font-family: 'Audiowide', 'Space Grotesk', sans-serif;
		letter-spacing: 1px;
		font-size: 1rem;
		text-transform: uppercase;
	}

	.submit-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(168, 178, 255, 0.3);
	}

	.form-note {
		margin-top: 0.75rem;
		font-size: 0.8rem;
		color: rgba(255, 255, 255, 0.6);
		text-align: center;
		line-height: 1.4;
	}

	@media (max-width: 768px) {
		.input-group {
			flex-direction: column;
		}

		.submit-btn {
			width: 100%;
		}
	}
</style>

<script>
	const form = document.getElementById('waitlistForm');

	form?.addEventListener('submit', async (e) => {
		e.preventDefault();

		const phoneInput = form.querySelector('input[type="tel"]') as HTMLInputElement;
		const phone = phoneInput.value;

		try {
			// Here you would typically send the phone to your backend
			alert('Transmission received. Prepare for cosmic contact.');
			phoneInput.value = '';
		} catch (error) {
			alert('Quantum interference detected. Please realign and try again.');
		}
	});
</script>