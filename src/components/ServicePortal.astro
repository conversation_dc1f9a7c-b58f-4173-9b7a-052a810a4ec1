---
interface Props {
	number: string;
	title: string;
	description: string;
}

const { number, title, description } = Astro.props;
---

<div class="service-portal">
	<div class="portal-number">{number}</div>
	<div class="portal-content">
		<h3>{title}</h3>
		<p>{description}</p>
	</div>
</div>

<style>
	.service-portal {
		background: rgba(255, 255, 255, 0.03);
		border-radius: 16px;
		padding: 2rem;
		display: flex;
		gap: 1.5rem;
		transition: transform 0.3s, box-shadow 0.3s, background 0.3s;
		border: 1px solid rgba(255, 255, 255, 0.08);
		margin-bottom: 1.5rem;
		backdrop-filter: blur(10px);
	}

	.service-portal:hover {
		transform: translateY(-5px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
		background: rgba(255, 255, 255, 0.05);
		border-color: rgba(168, 178, 255, 0.3);
	}

	.portal-number {
		font-size: 2.5rem;
		font-family: 'Audiowide', 'Space Grotesk', sans-serif;
		font-weight: 400;
		color: rgba(168, 178, 255, 0.4);
		line-height: 1;
		min-width: 60px;
		display: flex;
		align-items: flex-start;
		padding-top: 0.2rem;
		letter-spacing: 1px;
	}

	.portal-content {
		flex: 1;
	}

	h3 {
		margin: 0 0 0.75rem;
		font-size: 1.5rem;
		font-family: 'Audiowide', 'Space Grotesk', sans-serif;
		font-weight: 400;
		background: linear-gradient(45deg, #fff, #a8b2ff);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		line-height: 1.3;
		letter-spacing: 1px;
		text-transform: uppercase;
	}

	p {
		margin: 0;
		color: rgba(255, 255, 255, 0.8);
		line-height: 1.6;
		font-size: 1rem;
	}

	@media (max-width: 768px) {
		.service-portal {
			flex-direction: column;
			gap: 1rem;
			padding: 1.5rem;
		}

		.portal-number {
			font-size: 2rem;
			min-width: auto;
		}

		h3 {
			font-size: 1.3rem;
		}

		p {
			font-size: 0.95rem;
		}
	}
</style>
