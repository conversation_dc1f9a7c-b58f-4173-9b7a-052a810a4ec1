---
// Particle Field Component - Cosmic background with constellation patterns
---

<div class="particle-field">
  <canvas id="particleCanvas" class="particle-canvas"></canvas>
  <div class="constellation-overlay">
    <div class="constellation constellation-1"></div>
    <div class="constellation constellation-2"></div>
    <div class="constellation constellation-3"></div>
  </div>
</div>

<style>
  .particle-field {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    overflow: hidden;
  }

  .particle-canvas {
    width: 100%;
    height: 100%;
    display: block;
  }

  .constellation-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
  }

  .constellation {
    position: absolute;
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(168, 178, 255, 0.1) 0%, transparent 70%);
    animation: drift 30s linear infinite;
  }

  .constellation-1 {
    top: 10%;
    left: 20%;
    animation-duration: 25s;
  }

  .constellation-2 {
    top: 60%;
    right: 15%;
    animation-duration: 35s;
    animation-direction: reverse;
  }

  .constellation-3 {
    bottom: 20%;
    left: 10%;
    animation-duration: 40s;
  }

  @keyframes drift {
    0% {
      transform: translateX(0) translateY(0) rotate(0deg);
    }
    25% {
      transform: translateX(50px) translateY(-30px) rotate(90deg);
    }
    50% {
      transform: translateX(0) translateY(-60px) rotate(180deg);
    }
    75% {
      transform: translateX(-50px) translateY(-30px) rotate(270deg);
    }
    100% {
      transform: translateX(0) translateY(0) rotate(360deg);
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('particleCanvas') as HTMLCanvasElement;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    function resizeCanvas() {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    }
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle system
    interface Particle {
      x: number;
      y: number;
      vx: number;
      vy: number;
      size: number;
      opacity: number;
      color: string;
    }

    const particles: Particle[] = [];
    const particleCount = 150;
    const colors = ['#a8b2ff', '#ffffff', '#00ffff', '#ff00ff'];

    // Initialize particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 2 + 0.5,
        opacity: Math.random() * 0.8 + 0.2,
        color: colors[Math.floor(Math.random() * colors.length)]
      });
    }

    // Animation loop
    function animate() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw particles
      particles.forEach((particle, index) => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;

        // Draw particle
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();

        // Draw connections to nearby particles
        particles.slice(index + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 100) {
            ctx.save();
            ctx.globalAlpha = (1 - distance / 100) * 0.2;
            ctx.strokeStyle = particle.color;
            ctx.lineWidth = 0.5;
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.stroke();
            ctx.restore();
          }
        });
      });

      requestAnimationFrame(animate);
    }

    animate();

    // Mouse interaction
    let mouseX = 0;
    let mouseY = 0;

    canvas.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;

      // Attract nearby particles to mouse
      particles.forEach(particle => {
        const dx = mouseX - particle.x;
        const dy = mouseY - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 150) {
          const force = (150 - distance) / 150;
          particle.vx += (dx / distance) * force * 0.01;
          particle.vy += (dy / distance) * force * 0.01;
        }
      });
    });
  });
</script>
