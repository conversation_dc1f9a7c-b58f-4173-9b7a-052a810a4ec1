---
// Rive Logo Component for animated logo integration
---

<div class="rive-logo-container">
  <canvas id="rive-canvas" class="rive-canvas"></canvas>
  <div class="logo-glow"></div>
</div>

<style>
  .rive-logo-container {
    position: relative;
    height: 200px;
    margin: 0 auto 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .rive-canvas {
    height: 200px;
    border-radius: 50%;
    filter: drop-shadow(0 0 20px rgba(255, 0, 255, 0.4));
    transition: all 0.3s ease;
  }

  .rive-canvas:hover {
    filter: drop-shadow(0 0 30px rgba(168, 178, 255, 0.6));
    transform: scale(1.05);
  }

  .logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 220px;
    height: 220px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(168, 178, 255, 0.1) 0%, transparent 70%);
    animation: pulse 3s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.3;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  @media (max-width: 768px) {
    .rive-logo-container {
      width: 150px;
      height: 150px;
    }

    .logo-glow {
      width: 170px;
      height: 170px;
    }
  }
</style>

<script>
  import {Alignment, Fit, Layout, Rive} from '@rive-app/canvas'

  document.addEventListener('DOMContentLoaded', async () => {
    const canvas = document.getElementById('rive-canvas') as HTMLCanvasElement;

    if (canvas) {
      try {
        const r = new Rive({
          src: '/rea_logo.riv',
          canvas: canvas,
          autoplay: true,
          layout: new Layout({
            fit: Fit.Contain,
            alignment: Alignment.Center,
          }),
          onLoad: () => {
            console.log('REA Logo loaded successfully');
            r.resizeDrawingSurfaceToCanvas();
          },
          onLoadError: (error: any) => {
            console.error('Failed to load REA Logo:', error);
            // Fallback to logo-matching gradient representation
            canvas.style.background = 'linear-gradient(135deg, #ff00ff, #00ffff)';
            canvas.style.borderRadius = '50%';
            canvas.style.animation = 'spin 10s linear infinite';
          }
        });
      } catch (error) {
        console.error('Rive initialization error:', error);
        // Fallback to logo-matching gradient representation
        canvas.style.background = 'linear-gradient(135deg, #ff00ff, #00ffff)';
        canvas.style.borderRadius = '50%';
        canvas.style.animation = 'spin 10s linear infinite';

        // Add a simple CSS animation for the fallback
        const style = document.createElement('style');
        style.textContent = `
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }
        `;
        document.head.appendChild(style);
      }
    }
  });
</script>
