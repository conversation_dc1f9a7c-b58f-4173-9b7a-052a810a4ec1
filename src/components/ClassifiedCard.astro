---
// Classified Card Component - Covert-ops styled feature cards
interface Props {
  icon: string;
  title: string;
  description: string;
  classification?: 'UNCLASSIFIED' | 'CONFIDENTIAL' | 'SECRET' | 'TOP SECRET';
  missionCode?: string;
}

const { 
  icon, 
  title, 
  description, 
  classification = "CONFIDENTIAL",
  missionCode = "REA-" + Math.random().toString(36).substr(2, 6).toUpperCase()
} = Astro.props;
---

<div class="classified-card">
  <div class="card-header">
    <div class="classification-strip" data-level={classification.toLowerCase()}>
      <span class="classification-text">{classification}</span>
    </div>
    <div class="mission-code">
      <span class="code-label">MISSION:</span>
      <span class="code-value">{missionCode}</span>
    </div>
  </div>
  
  <div class="card-content">
    <div class="icon-container">
      <span class="card-icon">{icon}</span>
      <div class="icon-glow"></div>
    </div>
    
    <h3 class="card-title">{title}</h3>
    
    <div class="description-container">
      <p class="card-description" set:html={description}></p>
    </div>
  </div>
  
  <div class="card-footer">
    <div class="security-indicators">
      <span class="indicator">
        <span class="indicator-dot"></span>
        ENCRYPTED
      </span>
      <span class="indicator">
        <span class="indicator-dot"></span>
        VERIFIED
      </span>
    </div>
    <div class="access-level">
      <span class="access-text">CLEARANCE REQUIRED</span>
    </div>
  </div>
  
  <div class="scan-lines"></div>
</div>

<style>
  .classified-card {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 8px;
    padding: 0;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-family: 'Courier New', monospace;
  }

  .classified-card:hover {
    border-color: rgba(0, 255, 0, 0.6);
    box-shadow: 
      0 0 20px rgba(0, 255, 0, 0.2),
      inset 0 0 20px rgba(0, 255, 0, 0.05);
    transform: translateY(-5px);
  }

  .card-header {
    background: rgba(0, 255, 0, 0.1);
    border-bottom: 1px solid rgba(0, 255, 0, 0.3);
    padding: 0.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .classification-strip {
    padding: 0.2rem 0.8rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: bold;
  }

  .classification-strip[data-level="unclassified"] {
    background: rgba(0, 255, 0, 0.2);
    color: #00ff00;
  }

  .classification-strip[data-level="confidential"] {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff00;
  }

  .classification-strip[data-level="secret"] {
    background: rgba(255, 165, 0, 0.2);
    color: #ffa500;
  }

  .classification-strip[data-level="top secret"] {
    background: rgba(255, 0, 0, 0.2);
    color: #ff0000;
  }

  .mission-code {
    font-size: 0.7rem;
    color: rgba(0, 255, 0, 0.8);
  }

  .code-label {
    opacity: 0.7;
  }

  .code-value {
    font-weight: bold;
    margin-left: 0.5rem;
  }

  .card-content {
    padding: 2rem 1.5rem;
    text-align: center;
  }

  .icon-container {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
  }

  .card-icon {
    font-size: 3rem;
    display: block;
    filter: drop-shadow(0 0 10px rgba(0, 255, 0, 0.5));
  }

  .icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(0, 255, 0, 0.2) 0%, transparent 70%);
    animation: pulse-glow 3s ease-in-out infinite;
  }

  .card-title {
    color: #00ff00;
    font-size: 1.2rem;
    margin: 0 0 1rem 0;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .description-container {
    margin-bottom: 1.5rem;
  }

  .card-description {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
    font-size: 0.9rem;
    font-family: 'Space Grotesk', sans-serif;
  }

  .card-footer {
    background: rgba(0, 255, 0, 0.05);
    border-top: 1px solid rgba(0, 255, 0, 0.2);
    padding: 0.8rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .security-indicators {
    display: flex;
    gap: 1rem;
  }

  .indicator {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.7rem;
    color: rgba(0, 255, 0, 0.8);
  }

  .indicator-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #00ff00;
    animation: pulse-dot 2s ease-in-out infinite;
  }

  .access-level {
    font-size: 0.7rem;
    color: rgba(255, 255, 0, 0.8);
  }

  .scan-lines {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(0, 255, 0, 0.03) 2px,
      rgba(0, 255, 0, 0.03) 4px
    );
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .classified-card:hover .scan-lines {
    opacity: 1;
  }

  @keyframes pulse-glow {
    0%, 100% {
      opacity: 0.3;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.6;
      transform: translate(-50%, -50%) scale(1.1);
    }
  }

  @keyframes pulse-dot {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.3);
    }
  }

  @media (max-width: 768px) {
    .card-header {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }

    .card-content {
      padding: 1.5rem 1rem;
    }

    .card-footer {
      flex-direction: column;
      gap: 0.5rem;
      text-align: center;
    }

    .security-indicators {
      justify-content: center;
    }
  }
</style>
