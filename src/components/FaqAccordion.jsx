import { useState } from 'react';

const faqs = [
  {
    question: "Is this magic or math?",
    answer: "Both. Paradox is simply higher-order logic in disguise."
  },
  {
    question: "Can small teams really afford cosmic tech?",
    answer: "Yes. Our pay-as-you-grow model keeps cashflow cold while leads run hot—even cautious SMBs are warming up to AI when the ROI is real."
  },
  {
    question: "Will the Agency recruit me?",
    answer: "Decrypt the dossier hidden in your confirmation text. Pass the tests, join the ranks."
  },
  {
    question: "What makes Reality Engineering different?",
    answer: "Think Nick Fury's S.W.O.R.D., but for commerce—an extra-governmental guild safeguarding your market from algorithmic asteroids."
  },
  {
    question: "How do you measure success?",
    answer: "Generative messaging lifts click-throughs 3-4× for early adopters, per HubSpot & Salesforce studies; we wire those gains straight into your bottom line."
  }
];

export default function FaqAccordion() {
  const [openIndex, setOpenIndex] = useState(null);

  const toggleFaq = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  const baseStyles = {
    faqAccordion: {
      maxWidth: '800px',
      margin: '0 auto'
    },
    faqItem: {
      borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
      cursor: 'pointer',
      transition: 'background 0.3s'
    },
    faqQuestion: {
      padding: '1.5rem 0',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    faqQuestionH3: {
      margin: 0,
      fontSize: '1.2rem',
      fontWeight: 500,
      color: 'rgba(255, 255, 255, 0.9)',
      flex: 1,
      textAlign: 'left'
    },
    toggleIcon: {
      fontSize: '1.5rem',
      color: 'rgba(168, 178, 255, 0.8)',
      fontWeight: 300,
      marginLeft: '1rem',
      transition: 'transform 0.3s'
    },
    faqAnswer: {
      overflow: 'hidden',
      transition: 'max-height 0.3s ease, padding 0.3s ease'
    },
    faqAnswerP: {
      margin: 0,
      color: 'rgba(255, 255, 255, 0.7)',
      lineHeight: 1.6,
      fontSize: '1rem'
    }
  };

  return (
    <div style={baseStyles.faqAccordion}>
      {faqs.map((faq, index) => (
        <div
          key={index}
          style={{
            ...baseStyles.faqItem,
            background: openIndex === index ? 'rgba(255, 255, 255, 0.02)' : 'transparent'
          }}
          onClick={() => toggleFaq(index)}
          onMouseEnter={(e) => {
            if (openIndex !== index) {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.02)';
            }
          }}
          onMouseLeave={(e) => {
            if (openIndex !== index) {
              e.currentTarget.style.background = 'transparent';
            }
          }}
        >
          <div style={baseStyles.faqQuestion}>
            <h3 style={baseStyles.faqQuestionH3}>{faq.question}</h3>
            <span
              style={{
                ...baseStyles.toggleIcon,
                transform: openIndex === index ? 'rotate(180deg)' : 'rotate(0deg)'
              }}
            >
              {openIndex === index ? '−' : '+'}
            </span>
          </div>
          <div
            style={{
              ...baseStyles.faqAnswer,
              maxHeight: openIndex === index ? '200px' : '0',
              opacity: openIndex === index ? 1 : 0,
              paddingBottom: openIndex === index ? '1.5rem' : '0'
            }}
          >
            <p style={baseStyles.faqAnswerP}>{faq.answer}</p>
          </div>
        </div>
      ))}
    </div>
  );
}
