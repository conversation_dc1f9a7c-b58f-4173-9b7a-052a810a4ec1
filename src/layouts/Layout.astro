---
interface Props {
	title: string;
}

const { title } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
		<!-- Rive Animation Library -->
		<script src="https://unpkg.com/@rive-app/canvas@2.7.0/rive.js"></script>
	</head>
	<body>
		<slot />
	</body>
</html>

<style is:global>
	:root {
		--accent: 136, 58, 234;
		--accent-light: 224, 204, 250;
		--accent-dark: 49, 10, 101;
		--accent-gradient: linear-gradient(45deg, rgb(var(--accent)), rgb(var(--accent-light)) 30%, white 60%);
		--terminal-green: #00ff00;
		--terminal-cyan: #00ffff;
		--terminal-magenta: #ff00ff;
		--cosmic-purple: #a8b2ff;
		--quantum-blue: #6e7bff;
		/* Logo-matching colors */
		--logo-pink: #ff00ff;
		--logo-cyan: #00ffff;
		--logo-gradient: linear-gradient(135deg, #ff00ff, #00ffff);
		--logo-glow-pink: rgba(255, 0, 255, 0.4);
		--logo-glow-cyan: rgba(0, 255, 255, 0.4);
	}

	html {
		font-family: 'Space Grotesk', system-ui, sans-serif;
		background: #000000;
		background-image:
			radial-gradient(circle at 20% 80%, rgba(168, 178, 255, 0.1) 0%, transparent 50%),
			radial-gradient(circle at 80% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
			radial-gradient(circle at 40% 40%, rgba(255, 0, 255, 0.1) 0%, transparent 50%);
		background-size: 100% 100%;
		scroll-behavior: smooth;
		overflow-x: hidden;
	}

	body {
		margin: 0;
		padding: 0;
		color: #fff;
		position: relative;
	}

	* {
		box-sizing: border-box;
	}

	/* Enhanced typography for covert-ops theme */
	h1 {
		font-family: 'Space Grotesk', sans-serif;
		font-weight: 700;
		text-transform: uppercase;
		letter-spacing: 2px;
	}

	h2 {
		font-size: 2.5rem;
		margin: 4rem 0 2rem;
		text-align: center;
		background: var(--logo-gradient);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		font-family: 'Space Grotesk', sans-serif;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 1px;
	}

	h3 {
		color: var(--terminal-green);
		font-family: 'Courier Prime', monospace;
		text-transform: uppercase;
		letter-spacing: 1px;
	}

	section {
		position: relative;
	}

	/* Covert-ops UI elements */
	.glitch-text {
		animation: glitch 2s infinite;
	}

	@keyframes glitch {
		0%, 100% {
			text-shadow: 0 0 5px var(--terminal-green);
		}
		25% {
			text-shadow: -2px 0 var(--terminal-cyan), 2px 0 var(--terminal-magenta);
		}
		50% {
			text-shadow: 2px 0 var(--terminal-cyan), -2px 0 var(--terminal-magenta);
		}
		75% {
			text-shadow: 0 0 5px var(--terminal-green);
		}
	}

	/* Scrollbar styling */
	::-webkit-scrollbar {
		width: 8px;
	}

	::-webkit-scrollbar-track {
		background: rgba(0, 0, 0, 0.5);
	}

	::-webkit-scrollbar-thumb {
		background: var(--logo-gradient);
		border-radius: 4px;
	}

	::-webkit-scrollbar-thumb:hover {
		background: linear-gradient(135deg, var(--logo-cyan), var(--logo-pink));
	}

	@media (max-width: 768px) {
		h2 {
			font-size: 2rem;
		}
	}
</style>