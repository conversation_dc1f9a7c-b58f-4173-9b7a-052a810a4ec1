---
import Layout from '../layouts/Layout.astro';
import RiveLogo from '../components/RiveLogo.astro';
import NovaAssistant from '../components/NovaAssistant.astro';
import CovertTerminal from '../components/CovertTerminal.astro';
import ParticleField from '../components/ParticleField.astro';
import ClassifiedCard from '../components/ClassifiedCard.astro';
import WaitlistForm from '../components/WaitlistForm.astro';
import ParadoxTaglines from '../components/ParadoxTaglines.jsx';
import ServicePortal from '../components/ServicePortal.astro';
import FaqAccordion from '../components/FaqAccordion.jsx';
---

<Layout title="Reality Engineering | Where Cosmic Vision Meets Earthly Revenue">
	<!-- Particle Field Background -->
	<ParticleField />

	<!-- Nova AI Assistant -->
	<NovaAssistant />

	<main class="container">
		<!-- Hero Section with Mission Briefing -->
		<section class="hero-section">
			<div class="mission-header">
				<div class="classification-banner">
					<span class="classification-text">CONFIDENTIAL</span>
					<span class="mission-id">OPERATION: ENGINEER REALITY</span>
					<span class="timestamp" id="missionTime"></span>
				</div>
			</div>

			<div class="hero-content">
				<RiveLogo />

				<h1 class="title glitch-text">REALITY ENGINEERING AGENCY</h1>
				<p class="tagline">A paradox-powered growth agency that is both everywhere and unseen—as familiar as your phone buzz and as mysterious as dark energy.</p>

				<WaitlistForm />

				<ParadoxTaglines client:load />

				<CovertTerminal title="MISSION BRIEFING" classification="CONFIDENTIAL">
					<p>From omni-channel AI automations that text, talk, and teleport your brand into customers' hands to immersive storyworlds that magnetize loyalty, REA turns cosmic vision into earthly revenue.</p>
					<br />
					<p><span style="color: var(--terminal-green);">OBJECTIVE:</span> Deploy quantum-grade marketing operations with covert precision.</p>
					<p><span style="color: var(--terminal-green);">CLEARANCE:</span> Cosmic vision meets earthly revenue.</p>
				</CovertTerminal>

			</div>
		</section>

		<!-- Classified Operations Section -->
		<section class="classified-ops">
			<CovertTerminal title="CLASSIFIED OPERATIONS" classification="SECRET" promptPath="reality-engineering/operations" terminalId="terminal-operations">
				<h2 style="margin-top: 0; color: var(--terminal-green);">Why Creators Choose Our Constellation</h2>
				<p>Intelligence briefing on operational capabilities and strategic advantages.</p>

				<div class="embedded-features">
					<ClassifiedCard
						icon="✨"
						title="From Stardust to Starlight"
						description="Your raw creative atoms compressed into sales-supernova funnels through AI sequences that <a href='https://www.verizon.com/about/news/2025-state-small-business-survey' target='_blank' rel='noopener noreferrer' class='stat-link'>38% of SMBs already swear by</a>."
						classification="CONFIDENTIAL"
						missionCode="REA-STARDUST"
					/>
					<ClassifiedCard
						icon="🛡️"
						title="First Line of Delight, Last Line of Defense"
						description="We deploy ops as quietly as Langley but guard your brand integrity with CIA-grade rigor."
						classification="SECRET"
						missionCode="REA-SHIELD"
					/>
					<ClassifiedCard
						icon="📊"
						title="Proof, Not Promises"
						description="Generative messaging lifts click-throughs 3-4× for early adopters; we wire those gains straight into your bottom line."
						classification="CONFIDENTIAL"
						missionCode="REA-METRICS"
					/>
				</div>
			</CovertTerminal>
		</section>

		<!-- Mission Portals Section -->
		<section class="mission-portals">
			<CovertTerminal title="MISSION PORTALS" classification="TOP SECRET" promptPath="reality-engineering/portals" terminalId="terminal-portals">
				<h2 style="margin-top: 0; color: var(--terminal-green);">Three Portals, One Pushbutton</h2>
				<p>Access classified operational modules. Clearance level determines available features.</p>

				<div class="embedded-portals">
					<ServicePortal
						number="01"
						title="AI Automations & Omni-Channel Messaging"
						description="RCS, SMS, email, socials—one intelligence speaks fluent customer. 24/7 outreach with quantum precision."
					/>
					<ServicePortal
						number="02"
						title="Engaging Storytelling & Immersive Experiences"
						description="Interactive storyworlds drop shoppers into a branded multiverse; immersion becomes revenue."
					/>
					<ServicePortal
						number="03"
						title="Covert Innovation Lab"
						description="Custom agents, volumetric media, and sacred-geometry UX too classified to list—available to academy graduates."
					/>
				</div>
			</CovertTerminal>
		</section>

		<!-- Transmission Section -->
		<section class="transmission">
			<CovertTerminal title="INCOMING TRANSMISSION" classification="UNCLASSIFIED" promptPath="reality-engineering/broadcast" terminalId="terminal-broadcast">
				<h2 style="margin-top: 0; color: var(--terminal-green);">Reality Engineering Podcast // Loading...</h2>
				<p>Tune in Q3 2025 for transmissions that fuse Carl Sagan's "we are star-stuff" wonder with concrete conversion hacks.</p>
				<div style="margin-top: 1rem; color: var(--terminal-cyan);">
					<p>► SIGNAL STRENGTH: ████████░░ 80%</p>
					<p>► ENCRYPTION: QUANTUM-GRADE</p>
					<p>► STATUS: PREPARING BROADCAST</p>
				</div>
			</CovertTerminal>
		</section>

		<!-- Intelligence FAQ -->
		<section class="intelligence-faq">
			<CovertTerminal title="INTELLIGENCE FAQ" classification="CONFIDENTIAL" promptPath="reality-engineering/faq" terminalId="terminal-faq">
				<h2 style="margin-top: 0; color: var(--terminal-green);">FAQ – Paradox Edition</h2>
				<p>Classified intelligence briefings on operational procedures and cosmic protocols.</p>

				<div class="embedded-faq">
					<FaqAccordion client:visible />
				</div>
			</CovertTerminal>
		</section>

		<!-- Final Mission CTA -->
		<section class="final-mission">
			<CovertTerminal title="MISSION ACTIVATION" classification="TOP SECRET" promptPath="reality-engineering/deploy" terminalId="terminal-deploy">
				<h2 style="margin-top: 0; color: var(--terminal-green);">Reality won't bend itself.</h2>
				<p>Dial in. Load out. Let's make stardust profitable.</p>
				<div style="margin-top: 1rem; color: var(--terminal-cyan);">
					<p>► MISSION STATUS: AWAITING OPERATIVE</p>
					<p>► CLEARANCE: COSMIC VISION REQUIRED</p>
					<p>► DEPLOYMENT: IMMEDIATE</p>
				</div>

				<div class="embedded-waitlist">
					<WaitlistForm />
				</div>
			</CovertTerminal>
		</section>
	</main>
</Layout>

<style>
	.container {
		min-height: 100vh;
		position: relative;
		z-index: 10;
		max-width: 1400px;
		margin: 0 auto;
		padding: 2rem;
	}

	/* Hero Section */
	.hero-section {
		text-align: center;
		position: relative;
	}

	.mission-header {
		margin-bottom: 3rem;
	}

	.classification-banner {
		background: rgba(0, 0, 0, 0.9);
		border: 1px solid var(--terminal-green);
		border-radius: 8px;
		padding: 1rem 2rem;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-family: 'Courier Prime', monospace;
		font-size: 0.9rem;
		margin-bottom: 2rem;
		backdrop-filter: blur(10px);
	}

	.classification-text {
		color: var(--terminal-green);
		font-weight: bold;
		background: rgba(0, 255, 0, 0.1);
		padding: 0.3rem 0.8rem;
		border-radius: 4px;
	}

	.mission-id {
		background: var(--logo-gradient);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		font-family: 'Audiowide', 'Space Grotesk', sans-serif;
		font-weight: 400;
		text-transform: uppercase;
		letter-spacing: 2px;
		min-height: 1.5rem;
	}

	.timestamp {
		color: rgba(0, 255, 255, 0.8);
		font-size: 0.8rem;
	}

	.hero-content {
		position: relative;
	}

	.title {
		font-family: 'Audiowide', 'Space Grotesk', sans-serif;
		font-size: 5rem;
		font-weight: 400;
		margin: 0;
		background: var(--logo-gradient);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		text-shadow: 0 0 30px var(--logo-glow-pink);
		letter-spacing: 3px;
		min-height: 5rem;
	}

	.tagline {
		font-size: 1.4rem;
		color: rgba(255, 255, 255, 0.9);
		margin-bottom: 3rem;
		max-width: 900px;
		margin-left: auto;
		margin-right: auto;
		line-height: 1.5;
		font-weight: 300;
		min-height: 2.1rem;
	}

	/* Sections */
	.classified-ops,
	.mission-portals,
	.transmission,
	.intelligence-faq,
	.final-mission {
		margin: 6rem 0;
		position: relative;
	}

	.features {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
		margin-top: 3rem;
	}

	/* Embedded features inside terminal */
	:global(.embedded-features) {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
		gap: 1.5rem;
		margin-top: 2rem;
		padding: 1rem 0;
	}

	/* Adjust ClassifiedCard styling when embedded in terminal */
	:global(.embedded-features .classified-card) {
		background: rgba(0, 0, 0, 0.6);
		border: 1px solid rgba(0, 255, 0, 0.2);
		backdrop-filter: blur(5px);
		position: relative;
		overflow: hidden;
	}

	:global(.embedded-features .classified-card::before) {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: repeating-linear-gradient(
			90deg,
			transparent,
			transparent 2px,
			rgba(0, 255, 0, 0.02) 2px,
			rgba(0, 255, 0, 0.02) 4px
		);
		pointer-events: none;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	:global(.embedded-features .classified-card:hover::before) {
		opacity: 1;
	}

	:global(.embedded-features .classified-card:hover) {
		border-color: rgba(0, 255, 0, 0.4);
		box-shadow:
			0 0 15px rgba(0, 255, 0, 0.15),
			inset 0 0 15px rgba(0, 255, 0, 0.03);
		transform: translateY(-3px);
	}

	:global(.embedded-features .card-header) {
		background: rgba(0, 255, 0, 0.08);
		border-bottom: 1px solid rgba(0, 255, 0, 0.2);
	}

	/* Terminal-style text effects for embedded cards */
	:global(.embedded-features .card-title) {
		text-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
	}

	/* Embedded portals inside terminal */
	:global(.embedded-portals) {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
		margin-top: 2rem;
		padding: 1rem 0;
	}

	/* Adjust ServicePortal styling when embedded in terminal */
	:global(.embedded-portals .service-portal) {
		background: rgba(0, 0, 0, 0.6);
		border: 1px solid rgba(0, 255, 0, 0.2);
		backdrop-filter: blur(5px);
		position: relative;
		overflow: hidden;
	}

	:global(.embedded-portals .service-portal::before) {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: repeating-linear-gradient(
			90deg,
			transparent,
			transparent 2px,
			rgba(0, 255, 0, 0.02) 2px,
			rgba(0, 255, 0, 0.02) 4px
		);
		pointer-events: none;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	:global(.embedded-portals .service-portal:hover::before) {
		opacity: 1;
	}

	:global(.embedded-portals .service-portal:hover) {
		border-color: rgba(0, 255, 0, 0.4);
		box-shadow:
			0 0 15px rgba(0, 255, 0, 0.15),
			inset 0 0 15px rgba(0, 255, 0, 0.03);
		transform: translateY(-3px);
	}

	/* Terminal-style portal numbers */
	:global(.embedded-portals .portal-number) {
		color: rgba(0, 255, 0, 0.6);
		text-shadow: 0 0 5px rgba(0, 255, 0, 0.3);
	}

	/* Terminal-style portal titles */
	:global(.embedded-portals h3) {
		background: linear-gradient(45deg, #00ff00, #a8b2ff);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		text-shadow: 0 0 5px rgba(0, 255, 0, 0.2);
	}

	/* Embedded FAQ inside terminal */
	:global(.embedded-faq) {
		margin-top: 2rem;
		padding: 1rem 0;
	}

	/* Embedded waitlist inside terminal */
	:global(.embedded-waitlist) {
		margin-top: 2rem;
		padding: 1rem 0;
	}

	/* Adjust FAQ accordion styling when embedded in terminal */
	:global(.embedded-faq .faq-item) {
		background: rgba(0, 0, 0, 0.4);
		border: 1px solid rgba(0, 255, 0, 0.2);
		backdrop-filter: blur(5px);
	}

	:global(.embedded-faq .faq-item:hover) {
		border-color: rgba(0, 255, 0, 0.4);
		box-shadow: 0 0 10px rgba(0, 255, 0, 0.1);
	}

	/* Adjust waitlist form styling when embedded in terminal */
	:global(.embedded-waitlist .waitlist-form) {
		background: rgba(0, 0, 0, 0.3);
		border: 1px solid rgba(0, 255, 0, 0.2);
		border-radius: 8px;
		padding: 1.5rem;
		backdrop-filter: blur(5px);
	}

	.service-portals {
		display: flex;
		flex-direction: column;
		gap: 2rem;
		margin-top: 3rem;
	}

	/* Enhanced link styling */
	:global(.stat-link) {
		color: var(--logo-cyan);
		text-decoration: underline;
		text-decoration-color: var(--logo-glow-cyan);
		transition: all 0.3s ease;
		font-weight: 500;
	}

	:global(.stat-link:hover) {
		color: var(--logo-pink);
		text-decoration-color: var(--logo-pink);
		text-shadow: 0 0 5px var(--logo-glow-pink);
	}

	/* Responsive Design */
	@media (max-width: 1024px) {
		.container {
			padding: 1.5rem;
		}

		.features {
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		}

		:global(.embedded-features) {
			grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
			gap: 1.2rem;
		}

		:global(.embedded-portals) {
			gap: 1.2rem;
		}

		:global(.embedded-faq) {
			padding: 0.8rem 0;
		}

		:global(.embedded-waitlist) {
			padding: 0.8rem 0;
		}
	}

	@media (max-width: 768px) {
		.container {
			padding: 1rem;
		}

		.title {
			font-size: 3rem;
			letter-spacing: 2px;
		}

		.tagline {
			font-size: 1.2rem;
		}

		.classification-banner {
			flex-direction: column;
			gap: 1rem;
			text-align: center;
			padding: 1rem;
		}

		.features {
			grid-template-columns: 1fr;
		}

		:global(.embedded-features) {
			grid-template-columns: 1fr;
			gap: 1rem;
			padding: 0.5rem 0;
		}

		:global(.embedded-portals) {
			gap: 1rem;
			padding: 0.5rem 0;
		}

		:global(.embedded-faq) {
			padding: 0.5rem 0;
		}

		:global(.embedded-waitlist) {
			padding: 0.5rem 0;
		}

		:global(.embedded-waitlist .waitlist-form) {
			padding: 1rem;
		}

		.classified-ops,
		.mission-portals,
		.transmission,
		.intelligence-faq,
		.final-mission {
			margin: 4rem 0;
		}
	}

	@media (max-width: 480px) {
		.title {
			font-size: 2.5rem;
			letter-spacing: 1px;
		}

		.tagline {
			font-size: 1.1rem;
		}

		.classification-banner {
			font-size: 0.8rem;
		}
	}
</style>

<script>
	document.addEventListener('DOMContentLoaded', () => {
		// Update mission timestamp
		const missionTimeElement = document.getElementById('missionTime');

		function updateMissionTime() {
			if (missionTimeElement) {
				const now = new Date();
				const timestamp = now.toISOString().replace('T', ' ').substring(0, 19) + ' UTC';
				missionTimeElement.textContent = timestamp;
			}
		}

		updateMissionTime();
		setInterval(updateMissionTime, 1000);

		// Slower typewriter effect for above-the-fold content
		function typewriterEffect(element: HTMLElement, text: string, speed: number = 25) {
			element.textContent = '';
			let i = 0;

			function typeChar() {
				if (i < text.length) {
					element.textContent += text.charAt(i);
					i++;
					setTimeout(typeChar, speed + Math.random() * 15);
				}
			}

			typeChar();
		}

		// Initialize above-the-fold typewriter effects
		const title = document.querySelector('.title') as HTMLElement;
		const tagline = document.querySelector('.tagline') as HTMLElement;
		const missionId = document.querySelector('.mission-id') as HTMLElement;

		if (title && tagline && missionId) {
			// Store original text
			const titleText = title.textContent || '';
			const taglineText = tagline.textContent || '';
			const missionIdText = missionId.textContent || '';

			// Start typewriter effects with slower, more dramatic timing
			setTimeout(() => {
				typewriterEffect(title, titleText, 35);
			}, 1000);

			setTimeout(() => {
				typewriterEffect(tagline, taglineText, 20);
			}, titleText.length * 35 + 1500);

			setTimeout(() => {
				typewriterEffect(missionId, missionIdText, 25);
			}, titleText.length * 35 + taglineText.length * 20 + 2000);

			// Add interactive effects after typewriter completes
			setTimeout(() => {
				title.addEventListener('mouseenter', () => {
					title.style.textShadow = '0 0 50px rgba(255, 0, 255, 0.8), 0 0 80px rgba(0, 255, 255, 0.6)';
				});

				title.addEventListener('mouseleave', () => {
					title.style.textShadow = '0 0 30px rgba(255, 0, 255, 0.4)';
				});
			}, titleText.length * 35 + taglineText.length * 20 + missionIdText.length * 25 + 2500);
		}
	});
</script>
